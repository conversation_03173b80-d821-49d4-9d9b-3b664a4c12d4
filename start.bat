@echo off
echo Starting Tasbeeh App...

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist node_modules (
    echo Dependencies not found. Installing...
    call npm install
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to install dependencies.
        pause
        exit /b 1
    )
)

REM Start the application
npm start

if %ERRORLEVEL% neq 0 (
    echo Failed to start the application.
    pause
    exit /b 1
)