/* General Styles */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Roboto:wght@300;400;500&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', 'Segoe UI', sans-serif;
  background-color: transparent;
  color: #fff;
  overflow: hidden;
  user-select: none;
}

.app-container {
  width: 300px;
  height: 500px;
  background-color: rgba(25, 28, 41, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Header Styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: rgba(30, 34, 49, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.app-header h1 {
  font-family: '<PERSON><PERSON>', serif;
  font-size: 24px;
  font-weight: 700;
  color: #4ecca3;
}

.app-controls {
  display: flex;
  gap: 10px;
}

.icon-btn {
  background: none;
  border: none;
  color: #a0a0a0;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.icon-btn:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Total Counter Styles */
.total-counter {
  text-align: center;
  padding: 15px;
  background-color: rgba(30, 34, 49, 0.5);
}

.total-counter h2 {
  font-family: 'Amiri', serif;
  font-size: 18px;
  margin-bottom: 5px;
  color: #4ecca3;
}

.count-display {
  font-size: 32px;
  font-weight: 500;
  color: #fff;
  text-shadow: 0 0 10px rgba(78, 204, 163, 0.5);
}

/* Tasbeeh Container Styles */
.tasbeeh-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.tasbeeh-card {
  background-color: rgba(40, 44, 59, 0.8);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.tasbeeh-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-color: rgba(78, 204, 163, 0.3);
}

.tasbeeh-text {
  font-family: 'Amiri', serif;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: #fff;
  text-align: center;
}

.tasbeeh-translation {
  font-size: 14px;
  color: #4ecca3;
  margin-bottom: 3px;
}

.tasbeeh-meaning {
  font-size: 12px;
  color: #a0a0a0;
  margin-bottom: 10px;
  text-align: center;
}

.counter {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(78, 204, 163, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px 0;
  border: 2px solid rgba(78, 204, 163, 0.5);
}

.counter .count {
  font-size: 24px;
  font-weight: 500;
  color: #fff;
}

.card-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  width: 100%;
}

.count-btn, .reset-btn {
  padding: 8px 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  flex: 1;
}

.count-btn {
  background-color: #4ecca3;
  color: #1e2231;
  font-weight: 500;
}

.count-btn:hover {
  background-color: #3db390;
}

.reset-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: #a0a0a0;
}

.reset-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
}

/* Settings Panel Styles */
.settings-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(25, 28, 41, 0.98);
  padding: 20px;
  display: flex;
  flex-direction: column;
  z-index: 10;
  transition: transform 0.3s ease;
}

.settings-panel.hidden {
  transform: translateX(100%);
}

.settings-panel h3 {
  font-size: 20px;
  margin-bottom: 20px;
  color: #4ecca3;
  text-align: center;
}

.setting-item {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.setting-item label {
  font-size: 14px;
  color: #fff;
}

input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #4ecca3;
}

input[type="number"] {
  width: 60px;
  padding: 5px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  text-align: center;
}

.setting-actions {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

#reset-all-btn, #save-settings-btn {
  padding: 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

#reset-all-btn {
  background-color: rgba(255, 99, 71, 0.2);
  color: #ff6347;
  border: 1px solid rgba(255, 99, 71, 0.3);
}

#reset-all-btn:hover {
  background-color: rgba(255, 99, 71, 0.3);
}

#save-settings-btn {
  background-color: #4ecca3;
  color: #1e2231;
  font-weight: 500;
}

#save-settings-btn:hover {
  background-color: #3db390;
}

/* Scrollbar Styles */
::-webkit-scrollbar {
  width: 5px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(78, 204, 163, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(78, 204, 163, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(78, 204, 163, 0);
  }
}

.counter.animate {
  animation: pulse 0.5s ease-out;
}