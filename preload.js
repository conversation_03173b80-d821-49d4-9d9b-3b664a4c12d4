// Preload script for Electron
const { ipc<PERSON>ender<PERSON>, contextBridge } = require('electron');

console.log('Preload script running...');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('tasbeehAPI', {
  // Get tasbeeh data from main process
  getTasbeehData: () => {
    console.log('getTasbeehData called');
    ipcRenderer.send('get-tasbeeh-data');
  },
  
  // Update tasbeeh count
  updateCount: (type) => {
    console.log('updateCount called with type:', type);
    ipcRenderer.send('update-tasbeeh-count', type);
  },
  
  // Reset tasbeeh count
  resetCount: (type) => {
    console.log('resetCount called with type:', type);
    ipcRenderer.send('reset-tasbeeh-count', type);
  },
  
  // Update reminder settings
  updateReminderSettings: (settings) => {
    console.log('updateReminderSettings called with settings:', settings);
    ipcRenderer.send('update-reminder-settings', settings);
  },
  
  // Listen for tasbeeh data updates
  onTasbeehDataReceived: (callback) => {
    console.log('onTasbeehDataReceived listener set up');
    ipcRenderer.on('tasbeeh-data', (_, data) => {
      console.log('tasbeeh-data received:', data);
      callback(data);
    });
  },
  
  // Listen for tasbeeh data updates
  onTasbeehDataUpdated: (callback) => {
    console.log('onTasbeehDataUpdated listener set up');
    ipcRenderer.on('tasbeeh-data-updated', (_, data) => {
      console.log('tasbeeh-data-updated received:', data);
      callback(data);
    });
  }
});

console.log('Preload script completed, tasbeehAPI exposed');