{"rustc": 1842507548689473721, "features": "[\"system-tray\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 5244069873039314212, "deps": [[3150220818285335163, "url", false, 3986768155777021204], [4381063397040571828, "webview2_com", false, 14241958914699621486], [4405182208873388884, "http", false, 13132315702932854556], [7653476968652377684, "windows", false, 8740420095277714223], [8008191657135824715, "thiserror", false, 351791496888376248], [8292277814562636972, "tauri_utils", false, 4438534981889278773], [8319709847752024821, "uuid", false, 12987815471188456332], [8866577183823226611, "http_range", false, 13072063119145124644], [9689903380558560274, "serde", false, 1133695940199988036], [11693073011723388840, "raw_window_handle", false, 2897950004694174780], [13208667028893622512, "rand", false, 10658941434961289019], [14162324460024849578, "build_script_build", false, 16029095041485202634], [15367738274754116744, "serde_json", false, 6529998620451953356]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-bdfd465d29c4c1c6\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}