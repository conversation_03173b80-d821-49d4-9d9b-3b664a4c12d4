C:\Users\<USER>\OneDrive - LAD\Documents\TraeProjects\Tasbeeh-App - Copy\tasbeeh-lite\src-tauri\target\debug\deps\markup5ever-5a74fd77ff193789.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/generated.rs C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/named_entities.rs

C:\Users\<USER>\OneDrive - LAD\Documents\TraeProjects\Tasbeeh-App - Copy\tasbeeh-lite\src-tauri\target\debug\deps\libmarkup5ever-5a74fd77ff193789.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/generated.rs C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/named_entities.rs

C:\Users\<USER>\OneDrive - LAD\Documents\TraeProjects\Tasbeeh-App - Copy\tasbeeh-lite\src-tauri\target\debug\deps\libmarkup5ever-5a74fd77ff193789.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/generated.rs C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/named_entities.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\data\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\interface\tree_builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\serialize.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\buffer_queue.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.11.0\util\smallcharset.rs:
C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/generated.rs:
C:\Users\<USER>\OneDrive\ -\ LAD\Documents\TraeProjects\Tasbeeh-App\ -\ Copy\tasbeeh-lite\src-tauri\target\debug\build\markup5ever-7d108b80400abd17\out/named_entities.rs:

# env-dep:OUT_DIR=C:\\Users\\<USER>\\OneDrive - LAD\\Documents\\TraeProjects\\Tasbeeh-App - Copy\\tasbeeh-lite\\src-tauri\\target\\debug\\build\\markup5ever-7d108b80400abd17\\out
