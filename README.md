# Tasbeeh App

A desktop application for Tasbeeh (Islamic remembrance) with reminder functionality. The app sits on the right side of the screen and expands on hover, allowing you to count different types of dhikr (remembrance phrases).

## Features

- Four common dhikr phrases with counters:
  - <PERSON> il<PERSON>a illa <PERSON> (لا إلَهَ إلاَّ اللهُ)
  - <PERSON><PERSON> (اللهُ أكْبَرُ)
  - <PERSON><PERSON><PERSON><PERSON><PERSON> (الْحَمْدُ للهِ)
  - <PERSON><PERSON><PERSON><PERSON> (سُبْحَانَ اللهِ)
- Reminder notifications at customizable intervals
- Minimalist interface that stays out of your way
- Automatic saving of counts
- Keyboard shortcuts (1-4 keys for quick counting)

## Installation

### Prerequisites

- [Node.js](https://nodejs.org/) (v14 or later)
- [npm](https://www.npmjs.com/) (usually comes with Node.js)

### Development Setup

1. Clone this repository or download the source code
2. Navigate to the project directory
3. Install dependencies:
   ```
   npm install
   ```
4. Start the application:
   ```
   npm start
   ```

### Building the Application

To create an executable for your platform:

```
npm run build
```

This will generate the application in the `dist` directory.

## Usage

- The app will start minimized in the system tray
- Click the tray icon to show the app
- Click the counter buttons or use number keys (1-4) to increment counts
- Hover over the app to expand it when it's minimized
- Access settings by clicking the gear icon

## Keyboard Shortcuts

- `1` - Increment "La ilaha illa Allah" counter
- `2` - Increment "Allahu Akbar" counter
- `3` - Increment "Alhamdulillah" counter
- `4` - Increment "SubhanAllah" counter
- `Esc` - Close settings panel

## License

MIT