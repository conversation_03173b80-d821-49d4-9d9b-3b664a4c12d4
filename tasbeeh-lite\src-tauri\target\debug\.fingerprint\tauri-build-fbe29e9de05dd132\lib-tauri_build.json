{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 18230763535265024057, "deps": [[4450062412064442726, "dirs_next", false, 16169018844688234679], [4899080583175475170, "semver", false, 1507849587694838372], [7468248713591957673, "cargo_toml", false, 18221485386989674151], [8292277814562636972, "tauri_utils", false, 13658694688809979102], [9689903380558560274, "serde", false, 1133695940199988036], [10301936376833819828, "json_patch", false, 17856388625968564158], [13077543566650298139, "heck", false, 17935265403102117161], [13625485746686963219, "anyhow", false, 2538208430436148716], [14189313126492979171, "tauri_winres", false, 11880291980519139127], [15367738274754116744, "serde_json", false, 14632655477554404351], [15622660310229662834, "walkdir", false, 1686008090590248200]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-fbe29e9de05dd132\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}