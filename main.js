const { app, B<PERSON>erWindow, Tray, <PERSON>u, screen, ipcMain, Notification, nativeImage } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window and tray objects
let mainWindow;
let tray;
let isQuitting = false;
let reminderInterval;

// Path for storing tasbeeh data
const userDataPath = app.getPath('userData');
const dataFilePath = path.join(userDataPath, 'tasbeeh-data.json');

// Default tasbeeh data
const defaultTasbeehData = {
  counts: {
    'La ilaha illa Allah': 0,
    '<PERSON><PERSON> Akbar': 0,
    'Al<PERSON><PERSON><PERSON><PERSON>': 0,
    'SubhanAllah': 0
  },
  totalCount: 0,
  reminderEnabled: true,
  reminderInterval: 60000 // 1 minute in milliseconds
};

// Load or initialize tasbeeh data
function loadTasbeehData() {
  try {
    if (fs.existsSync(dataFilePath)) {
      const data = fs.readFileSync(dataFilePath, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading tasbeeh data:', error);
  }
  
  // If file doesn't exist or there's an error, return default data
  return defaultTasbeehData;
}

// Save tasbeeh data
function saveTasbeehData(data) {
  try {
    // Ensure the directory exists
    if (!fs.existsSync(path.dirname(dataFilePath))) {
      fs.mkdirSync(path.dirname(dataFilePath), { recursive: true });
    }
    fs.writeFileSync(dataFilePath, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error saving tasbeeh data:', error);
  }
}

// Create the browser window
function createWindow() {
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;

  // Window dimensions for auto-hide functionality
  const fullWidth = 300;
  const hiddenWidth = 20; // Small part that remains visible
  const windowHeight = height; // Full screen height

  mainWindow = new BrowserWindow({
    width: fullWidth,
    height: windowHeight,
    x: width - fullWidth, // Position on the right side of the screen
    y: 0, // Start from top
    frame: false,
    transparent: true,
    resizable: false,
    skipTaskbar: true,
    alwaysOnTop: true, // Keep on top for better hover detection
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Load the index.html file
  mainWindow.loadFile('index.html');

  // Auto-hide functionality
  let isHidden = false;
  let hideTimeout;

  // Function to hide the window (slide to right)
  function hideWindow() {
    if (!isHidden) {
      isHidden = true;
      mainWindow.setBounds({
        x: width - hiddenWidth,
        y: 0,
        width: hiddenWidth,
        height: windowHeight
      });
    }
  }

  // Function to show the window (slide from right)
  function showWindow() {
    if (isHidden) {
      isHidden = false;
      mainWindow.setBounds({
        x: width - fullWidth,
        y: 0,
        width: fullWidth,
        height: windowHeight
      });
    }
  }

  // Mouse enter/leave detection
  mainWindow.on('enter-full-screen', () => {
    // This won't be used, but keeping for compatibility
  });

  // Use mouse position to detect hover
  const { globalShortcut } = require('electron');

  // Check mouse position periodically
  setInterval(() => {
    const mousePos = screen.getCursorScreenPoint();
    const windowBounds = mainWindow.getBounds();

    // Check if mouse is near the right edge of screen or over the window
    const isNearRightEdge = mousePos.x >= width - fullWidth;
    const isOverWindow = mousePos.x >= windowBounds.x &&
                        mousePos.x <= windowBounds.x + windowBounds.width &&
                        mousePos.y >= windowBounds.y &&
                        mousePos.y <= windowBounds.y + windowBounds.height;

    if (isNearRightEdge || isOverWindow) {
      // Clear any pending hide timeout
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        hideTimeout = null;
      }
      showWindow();
    } else {
      // Set timeout to hide window after mouse leaves
      if (!hideTimeout && !isHidden) {
        hideTimeout = setTimeout(() => {
          hideWindow();
          hideTimeout = null;
        }, 1000); // Hide after 1 second
      }
    }
  }, 100); // Check every 100ms

  // Initially hide the window after a short delay
  setTimeout(() => {
    hideWindow();
  }, 2000);

  // Prevent the window from being closed
  mainWindow.on('close', (event) => {
    if (!isQuitting) {
      event.preventDefault();
      mainWindow.hide();
      return false;
    }
    return true;
  });
}

// Create the tray icon
function createTray() {
  const trayIconPath = path.join(__dirname, 'assets', 'tray-icon.svg');
  const trayIcon = nativeImage.createFromPath(trayIconPath).resize({ width: 16, height: 16 });
  tray = new Tray(trayIcon);
  
  const contextMenu = Menu.buildFromTemplate([
    { 
      label: 'Open Tasbeeh App', 
      click: () => {
        mainWindow.show();
      } 
    },
    { 
      label: 'Toggle Reminder', 
      click: () => {
        const data = loadTasbeehData();
        data.reminderEnabled = !data.reminderEnabled;
        saveTasbeehData(data);
        setupReminderInterval(data);
        mainWindow.webContents.send('tasbeeh-data-updated', data);
      } 
    },
    { type: 'separator' },
    { 
      label: 'Quit', 
      click: () => {
        isQuitting = true;
        app.quit();
      } 
    }
  ]);

  tray.setToolTip('Tasbeeh App');
  tray.setContextMenu(contextMenu);

  // Show window when tray icon is clicked
  tray.on('click', () => {
    mainWindow.show();
  });
}

// Setup reminder notifications
function setupReminderInterval(data) {
  // Clear existing interval if any
  if (reminderInterval) {
    clearInterval(reminderInterval);
  }

  // Set up new interval if enabled
  if (data.reminderEnabled) {
    reminderInterval = setInterval(() => {
      showReminderNotification();
    }, data.reminderInterval);
  }
}

// Show reminder notification
function showReminderNotification() {
  const notificationIconPath = path.join(__dirname, 'assets', 'notification-icon.svg');
  const notificationIcon = nativeImage.createFromPath(notificationIconPath).resize({ width: 32, height: 32 });
  const notification = new Notification({
    title: 'Tasbeeh Reminder',
    body: 'Time to remember Allah. Take a moment for dhikr.',
    icon: notificationIcon
  });
  
  notification.show();
  notification.on('click', () => {
    if (mainWindow) {
      mainWindow.show();
    }
  });
}

// App ready event
app.whenReady().then(() => {
  // Load tasbeeh data
  const tasbeehData = loadTasbeehData();
  
  // Create window and tray
  createWindow();
  createTray();
  
  // Setup reminder interval
  setupReminderInterval(tasbeehData);

  // Mac OS specific: recreate window when dock icon is clicked
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    } else {
      mainWindow.show();
    }
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle before-quit event
app.on('before-quit', () => {
  isQuitting = true;
});

// IPC handlers for communication with renderer process
ipcMain.on('get-tasbeeh-data', (event) => {
  const data = loadTasbeehData();
  event.reply('tasbeeh-data', data);
});

ipcMain.on('update-tasbeeh-count', (event, type) => {
  const data = loadTasbeehData();
  
  if (data.counts.hasOwnProperty(type)) {
    data.counts[type] += 1;
    data.totalCount += 1;
    saveTasbeehData(data);
    event.reply('tasbeeh-data-updated', data);
  }
});

ipcMain.on('reset-tasbeeh-count', (event, type) => {
  const data = loadTasbeehData();
  
  if (type === 'all') {
    // Reset all counts
    Object.keys(data.counts).forEach(key => {
      data.totalCount -= data.counts[key];
      data.counts[key] = 0;
    });
  } else if (data.counts.hasOwnProperty(type)) {
    // Reset specific count
    data.totalCount -= data.counts[type];
    data.counts[type] = 0;
  }
  
  saveTasbeehData(data);
  event.reply('tasbeeh-data-updated', data);
});

ipcMain.on('update-reminder-settings', (event, settings) => {
  const data = loadTasbeehData();
  
  if (settings.hasOwnProperty('enabled')) {
    data.reminderEnabled = settings.enabled;
  }
  
  if (settings.hasOwnProperty('interval')) {
    data.reminderInterval = settings.interval;
  }
  
  saveTasbeehData(data);
  setupReminderInterval(data);
  event.reply('tasbeeh-data-updated', data);
});