{"name": "tasbeeh-app", "version": "1.0.0", "description": "A desktop application for Tasbeeh with reminder functionality", "main": "main.js", "scripts": {"start": "electron .", "build": "node build.js"}, "keywords": ["tasbeeh", "dhikr", "reminder", "islamic"], "author": "", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "build": {"appId": "com.tasbeeh.app", "productName": "Tasbeeh App", "win": {"target": "nsis", "icon": "build/icon.ico"}}}