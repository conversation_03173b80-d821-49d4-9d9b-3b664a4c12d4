<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Ta<PERSON><PERSON>h App</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="app-container">
    <!-- Hidden mode circular counter -->
    <div class="hidden-counter-container">
      <div class="hidden-counter-circle">
        <span id="hidden-counter">0</span>
      </div>
    </div>

    <div class="app-header">
      <h1>تسبيح</h1>
      <div class="app-controls">
        <button id="settings-btn" class="icon-btn" title="Settings">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
            <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115l.094-.319z"/>
          </svg>
        </button>
        <button id="minimize-btn" class="icon-btn" title="Minimize">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path fill-rule="evenodd" d="M2 8a.5.5 0 0 1 .5-.5h11a.5.5 0 0 1 0 1h-11A.5.5 0 0 1 2 8Z"/>
          </svg>
        </button>
      </div>
    </div>
    
    <div class="total-counter">
      <h2>إجمالي التسبيحات</h2>
      <div id="total-count" class="count-display">0</div>
    </div>
    
    <div class="tasbeeh-container">
      <div class="tasbeeh-card" data-type="La ilaha illa Allah">
        <div class="tasbeeh-text">لا إلَهَ إلاَّ اللهُ</div>
        <div class="tasbeeh-translation">La ilaha illa Allah</div>
        <div class="tasbeeh-meaning">There is no god but Allah</div>
        <div class="counter">
          <span class="count">0</span>
        </div>
        <div class="card-buttons">
          <button class="count-btn">Add</button>
          <button class="reset-btn">Reset</button>
        </div>
      </div>
      
      <div class="tasbeeh-card" data-type="Allahu Akbar">
        <div class="tasbeeh-text">اللهُ أكْبَرُ</div>
        <div class="tasbeeh-translation">Allahu Akbar</div>
        <div class="tasbeeh-meaning">Allah is the Greatest</div>
        <div class="counter">
          <span class="count">0</span>
        </div>
        <div class="card-buttons">
          <button class="count-btn">Add</button>
          <button class="reset-btn">Reset</button>
        </div>
      </div>
      
      <div class="tasbeeh-card" data-type="Alhamdulillah">
        <div class="tasbeeh-text">الْحَمْدُ للهِ</div>
        <div class="tasbeeh-translation">Alhamdulillah</div>
        <div class="tasbeeh-meaning">Praise be to Allah</div>
        <div class="counter">
          <span class="count">0</span>
        </div>
        <div class="card-buttons">
          <button class="count-btn">Add</button>
          <button class="reset-btn">Reset</button>
        </div>
      </div>
      
      <div class="tasbeeh-card" data-type="SubhanAllah">
        <div class="tasbeeh-text">سُبْحَانَ اللهِ</div>
        <div class="tasbeeh-translation">SubhanAllah</div>
        <div class="tasbeeh-meaning">Glory be to Allah</div>
        <div class="counter">
          <span class="count">0</span>
        </div>
        <div class="card-buttons">
          <button class="count-btn">Add</button>
          <button class="reset-btn">Reset</button>
        </div>
      </div>
    </div>
    
    <div id="settings-panel" class="settings-panel hidden">
      <h3>Settings</h3>
      <div class="setting-item">
        <label for="reminder-toggle">Enable Reminders</label>
        <input type="checkbox" id="reminder-toggle" checked>
      </div>
      <div class="setting-item">
        <label for="reminder-interval">Reminder Interval (minutes)</label>
        <input type="number" id="reminder-interval" min="1" max="60" value="1">
      </div>
      <div class="setting-actions">
        <button id="reset-all-btn">Reset All Counts</button>
        <button id="save-settings-btn">Save Settings</button>
      </div>
    </div>
  </div>
  
  <script src="renderer.js"></script>
</body>
</html>