{"rustc": 1842507548689473721, "features": "[\"objc-exception\", \"system-tray\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 1560672123662042931, "deps": [[4381063397040571828, "webview2_com", false, 14241958914699621486], [7653476968652377684, "windows", false, 8740420095277714223], [8292277814562636972, "tauri_utils", false, 4438534981889278773], [8319709847752024821, "uuid", false, 12987815471188456332], [8391357152270261188, "wry", false, 10822468987803563346], [11693073011723388840, "raw_window_handle", false, 2897950004694174780], [13208667028893622512, "rand", false, 10658941434961289019], [14162324460024849578, "tauri_runtime", false, 10239767877721743695], [16228250612241359704, "build_script_build", false, 15971506688331644004]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-adaca4f88abba700\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}