{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\", \"tray\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 8364703167176882655, "deps": [[3007252114546291461, "tao", false, 11567668838331736779], [3150220818285335163, "url", false, 3986768155777021204], [3540822385484940109, "windows_implement", false, 287656234716071550], [3722963349756955755, "once_cell", false, 11823707068682644266], [4381063397040571828, "webview2_com", false, 14241958914699621486], [4405182208873388884, "http", false, 13132315702932854556], [4684437522915235464, "libc", false, 9549968222755141508], [5986029879202738730, "log", false, 8715407772055090116], [7653476968652377684, "windows", false, 8740420095277714223], [8008191657135824715, "thiserror", false, 351791496888376248], [8391357152270261188, "build_script_build", false, 2227433315579039077], [9689903380558560274, "serde", false, 1133695940199988036], [11989259058781683633, "dunce", false, 7111642018276055303], [15367738274754116744, "serde_json", false, 6529998620451953356]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-a569e11d134820aa\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}