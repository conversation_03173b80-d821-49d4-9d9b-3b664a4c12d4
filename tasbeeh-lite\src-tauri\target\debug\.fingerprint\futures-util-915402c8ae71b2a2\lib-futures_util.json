{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 6118698086558795715, "deps": [[1615478164327904835, "pin_utils", false, 7701672662843469921], [1906322745568073236, "pin_project_lite", false, 15252274309612337884], [5451793922601807560, "slab", false, 9904727723980055234], [7620660491849607393, "futures_core", false, 17101099715901275366], [10565019901765856648, "futures_macro", false, 215633712264812196], [16240732885093539806, "futures_task", false, 2170934916370196221]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-915402c8ae71b2a2\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}