<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tasbeeh App - Installation Guide</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
    }
    h1 {
      color: #2e8b57;
      border-bottom: 2px solid #4ecca3;
      padding-bottom: 10px;
    }
    h2 {
      color: #2e8b57;
      margin-top: 30px;
    }
    .step {
      background-color: #f9f9f9;
      border-left: 4px solid #4ecca3;
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 0 5px 5px 0;
    }
    .step h3 {
      margin-top: 0;
      color: #2e8b57;
    }
    code {
      background-color: #f1f1f1;
      padding: 2px 5px;
      border-radius: 3px;
      font-family: Consol<PERSON>, Monaco, 'Andale Mono', monospace;
    }
    .note {
      background-color: #fffde7;
      border-left: 4px solid #ffd54f;
      padding: 15px;
      margin: 20px 0;
      border-radius: 0 5px 5px 0;
    }
    .button {
      display: inline-block;
      background-color: #4ecca3;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 5px;
      font-weight: bold;
      margin-top: 10px;
    }
    .button:hover {
      background-color: #2e8b57;
    }
  </style>
</head>
<body>
  <h1>Tasbeeh App - Installation Guide</h1>
  
  <p>Thank you for downloading the Tasbeeh App! This guide will help you install and run the application on your computer.</p>
  
  <div class="note">
    <strong>Note:</strong> This application requires Node.js to be installed on your computer. If you don't have it installed, please download and install it from <a href="https://nodejs.org/" target="_blank">https://nodejs.org/</a> before proceeding.
  </div>
  
  <h2>Installation Methods</h2>
  
  <div class="step">
    <h3>Method 1: Using the Installation Script (Recommended)</h3>
    <ol>
      <li>Double-click the <code>install.bat</code> file in the application folder.</li>
      <li>Wait for the script to install all necessary dependencies.</li>
      <li>When prompted, choose whether you want to build the application now.</li>
      <li>If you choose to build, wait for the build process to complete.</li>
      <li>Once completed, you can find the installer in the <code>dist</code> folder.</li>
    </ol>
  </div>
  
  <div class="step">
    <h3>Method 2: Manual Installation</h3>
    <ol>
      <li>Open a command prompt or terminal window.</li>
      <li>Navigate to the application folder using the <code>cd</code> command.</li>
      <li>Run the following command to install dependencies:
        <br><code>npm install</code></li>
      <li>After installation completes, you can build the application with:
        <br><code>npm run build</code></li>
      <li>The installer will be created in the <code>dist</code> folder.</li>
    </ol>
  </div>
  
  <h2>Running the Application</h2>
  
  <div class="step">
    <h3>Method 1: Using the Start Script</h3>
    <p>Double-click the <code>start.bat</code> file in the application folder.</p>
  </div>
  
  <div class="step">
    <h3>Method 2: Using npm</h3>
    <ol>
      <li>Open a command prompt or terminal window.</li>
      <li>Navigate to the application folder using the <code>cd</code> command.</li>
      <li>Run the following command:
        <br><code>npm start</code></li>
    </ol>
  </div>
  
  <div class="step">
    <h3>Method 3: Using the Installer</h3>
    <p>If you've built the application:</p>
    <ol>
      <li>Navigate to the <code>dist</code> folder.</li>
      <li>Run the installer (.exe file) and follow the installation prompts.</li>
      <li>After installation, you can launch the app from your Start menu or desktop shortcut.</li>
    </ol>
  </div>
  
  <h2>Troubleshooting</h2>
  
  <div class="step">
    <h3>Common Issues</h3>
    <ul>
      <li><strong>"'node' is not recognized as an internal or external command"</strong><br>
        Solution: Install Node.js from <a href="https://nodejs.org/" target="_blank">https://nodejs.org/</a></li>
      <li><strong>"Error: Cannot find module..."</strong><br>
        Solution: Run <code>npm install</code> to ensure all dependencies are installed.</li>
      <li><strong>Application doesn't appear after starting</strong><br>
        Solution: Check your system tray for the Tasbeeh App icon. Click it to show the application.</li>
    </ul>
  </div>
  
  <div class="note">
    <p>If you encounter any other issues, please check the README.md file for more information or contact the developer for support.</p>
  </div>
  
  <p>Thank you for using Tasbeeh App! May it assist you in your daily remembrance of Allah.</p>
</body>
</html>