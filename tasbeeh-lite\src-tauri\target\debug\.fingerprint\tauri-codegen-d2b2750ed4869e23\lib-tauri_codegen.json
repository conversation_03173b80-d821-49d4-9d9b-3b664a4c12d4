{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 17457209433574273112, "deps": [[3060637413840920116, "proc_macro2", false, 2861560310303848729], [4899080583175475170, "semver", false, 1507849587694838372], [7392050791754369441, "ico", false, 5912177696775521784], [8008191657135824715, "thiserror", false, 351791496888376248], [8292277814562636972, "tauri_utils", false, 13658694688809979102], [8319709847752024821, "uuid", false, 12987815471188456332], [9689903380558560274, "serde", false, 1133695940199988036], [9857275760291862238, "sha2", false, 7574765903621416492], [10301936376833819828, "json_patch", false, 17856388625968564158], [12687914511023397207, "png", false, 18167151222135113521], [14132538657330703225, "brotli", false, 1231801664303116538], [15367738274754116744, "serde_json", false, 14632655477554404351], [15622660310229662834, "walkdir", false, 1686008090590248200], [17990358020177143287, "quote", false, 10439889782410048161], [18066890886671768183, "base64", false, 4277722468282486681]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-d2b2750ed4869e23\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}