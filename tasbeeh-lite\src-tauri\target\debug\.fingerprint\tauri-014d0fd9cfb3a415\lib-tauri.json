{"rustc": 1842507548689473721, "features": "[\"app-hide\", \"app-show\", \"compression\", \"default\", \"dialog\", \"dialog-ask\", \"fs-create-dir\", \"fs-exists\", \"fs-read-file\", \"fs-write-file\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"rfd\", \"system-tray\", \"tauri-runtime-wry\", \"window-close\", \"window-hide\", \"window-minimize\", \"window-set-position\", \"window-set-size\", \"window-show\", \"window-start-dragging\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 3449039062919030793, "deps": [[40386456601120721, "percent_encoding", false, 5339315221659133369], [947818755262499932, "notify_rust", false, 7210580268749756873], [1260461579271933187, "serialize_to_javascript", false, 4721789789504349236], [1441306149310335789, "tempfile", false, 18381933214753224763], [3150220818285335163, "url", false, 3986768155777021204], [3722963349756955755, "once_cell", false, 11823707068682644266], [4381063397040571828, "webview2_com", false, 14241958914699621486], [4405182208873388884, "http", false, 13132315702932854556], [4450062412064442726, "dirs_next", false, 16169018844688234679], [4899080583175475170, "semver", false, 12478413967425834357], [5099504066399492044, "rfd", false, 18030311847678191811], [5180608563399064494, "tauri_macros", false, 5306981558067586814], [5610773616282026064, "build_script_build", false, 5117263354216864933], [5986029879202738730, "log", false, 8715407772055090116], [7653476968652377684, "windows", false, 8740420095277714223], [8008191657135824715, "thiserror", false, 351791496888376248], [8292277814562636972, "tauri_utils", false, 4438534981889278773], [8319709847752024821, "uuid", false, 12987815471188456332], [9623796893764309825, "ignore", false, 12578040584471888763], [9689903380558560274, "serde", false, 1133695940199988036], [9920160576179037441, "getrandom", false, 14149105672166510027], [10629569228670356391, "futures_util", false, 1497982187717778518], [11601763207901161556, "tar", false, 9123574207410837382], [11693073011723388840, "raw_window_handle", false, 2897950004694174780], [11989259058781683633, "dunce", false, 7111642018276055303], [12393800526703971956, "tokio", false, 9980392976430629456], [12986574360607194341, "serde_repr", false, 4081041780383406079], [13208667028893622512, "rand", false, 10658941434961289019], [13625485746686963219, "anyhow", false, 2538208430436148716], [14162324460024849578, "tauri_runtime", false, 10239767877721743695], [14564311161534545801, "encoding_rs", false, 13273357911133950413], [15367738274754116744, "serde_json", false, 6529998620451953356], [16228250612241359704, "tauri_runtime_wry", false, 16945057967660345497], [17155886227862585100, "glob", false, 6609885923755107645], [17278893514130263345, "state", false, 7536587351360768558], [17772299992546037086, "flate2", false, 10300550982006348987]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-014d0fd9cfb3a415\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}