const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create build directory if it doesn't exist
const buildDir = path.join(__dirname, 'build');
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir);
}

// Function to convert SVG to ICO using Electron's native image module
function convertSvgToIco() {
  console.log('Converting SVG icons to ICO format...');
  
  try {
    // Create a simple script to convert SVG to ICO
    const converterScript = `
      const { app, nativeImage } = require('electron');
      const fs = require('fs');
      const path = require('path');
      
      app.whenReady().then(() => {
        const svgPath = path.join(__dirname, 'assets', 'icon.svg');
        const icoPath = path.join(__dirname, 'build', 'icon.ico');
        
        // Read SVG file
        const svgBuffer = fs.readFileSync(svgPath);
        
        // Convert to ICO
        const img = nativeImage.createFromBuffer(svgBuffer);
        const resizedImg = img.resize({ width: 256, height: 256 });
        
        // Save as ICO
        fs.writeFileSync(icoPath, resizedImg.toPNG());
        
        console.log('Icon converted successfully');
        app.quit();
      });
    `;
    
    // Write the converter script to a temporary file
    const tempScriptPath = path.join(__dirname, 'temp-converter.js');
    fs.writeFileSync(tempScriptPath, converterScript);
    
    // Run the converter script with Electron
    console.log('Running converter script...');
    execSync(`npx electron ${tempScriptPath}`, { stdio: 'inherit' });
    
    // Clean up
    fs.unlinkSync(tempScriptPath);
    
    console.log('Icon conversion completed');
  } catch (error) {
    console.error('Error converting icons:', error);
    console.log('Continuing with build process...');
  }
}

// Main build function
async function build() {
  console.log('Starting build process...');
  
  // Convert SVG to ICO
  convertSvgToIco();
  
  // Run electron-builder
  console.log('Building application with electron-builder...');
  try {
    execSync('npx electron-builder', { stdio: 'inherit' });
    console.log('Build completed successfully!');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

// Run the build
build();