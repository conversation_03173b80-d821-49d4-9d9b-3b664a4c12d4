{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 17085485324736096367, "deps": [[561782849581144631, "html5ever", false, 942598453720835795], [3150220818285335163, "url", false, 3986768155777021204], [3334271191048661305, "windows_version", false, 13874325028892983492], [4071963112282141418, "serde_with", false, 4141999639112966495], [4899080583175475170, "semver", false, 12478413967425834357], [5986029879202738730, "log", false, 8715407772055090116], [6262254372177975231, "kuchiki", false, 17513970967945642814], [6606131838865521726, "ctor", false, 6292433937378726971], [6997837210367702832, "infer", false, 16424120086249414139], [8008191657135824715, "thiserror", false, 351791496888376248], [9689903380558560274, "serde", false, 1133695940199988036], [10301936376833819828, "json_patch", false, 10490563364317450204], [11989259058781683633, "dunce", false, 7111642018276055303], [14132538657330703225, "brotli", false, 1231801664303116538], [15367738274754116744, "serde_json", false, 6529998620451953356], [15622660310229662834, "walkdir", false, 12777252158909855155], [15932120279885307830, "memchr", false, 6061289150125058545], [17155886227862585100, "glob", false, 6609885923755107645], [17186037756130803222, "phf", false, 14330515728788082103]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-18fd20fd49fa3474\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}