@echo off
echo Installing Tasbeeh App dependencies...

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
where npm >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo npm is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
npm install

if %ERRORLEVEL% neq 0 (
    echo Failed to install dependencies.
    pause
    exit /b 1
)

echo Dependencies installed successfully.

REM Ask if user wants to build the application
set /p BUILD_APP=Do you want to build the application now? (y/n): 

if /i "%BUILD_APP%"=="y" (
    echo Building application...
    npm run build
    
    if %ERRORLEVEL% neq 0 (
        echo Failed to build application.
        pause
        exit /b 1
    )
    
    echo Application built successfully. You can find the installer in the dist folder.
) else (
    echo You can build the application later by running 'npm run build'
)

echo.
echo To start the application in development mode, run: npm start
echo.

pause