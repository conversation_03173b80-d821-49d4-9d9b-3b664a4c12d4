{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 2387097677382181677, "deps": [[1462335029370885857, "quick_xml", false, 14665367527495841863], [3334271191048661305, "windows_version", false, 13874325028892983492], [10806645703491011684, "thiserror", false, 16460104134696615539], [14585479307175734061, "windows", false, 16484276593494792594]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-d36bed2a11d50a28\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}