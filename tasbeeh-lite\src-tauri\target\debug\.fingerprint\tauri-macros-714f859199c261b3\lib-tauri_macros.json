{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 13514017633217684152, "deps": [[2713742371683562785, "syn", false, 6684988258678334043], [3060637413840920116, "proc_macro2", false, 2861560310303848729], [8292277814562636972, "tauri_utils", false, 13658694688809979102], [13077543566650298139, "heck", false, 17935265403102117161], [17492769205600034078, "tauri_codegen", false, 7081564322022535452], [17990358020177143287, "quote", false, 10439889782410048161]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-714f859199c261b3\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}